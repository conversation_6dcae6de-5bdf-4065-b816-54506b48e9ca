# Bosch Products Data Loader

This document explains how to safely load Bosch products data from `bosch_products_output.json` into your e-commerce database.

## Overview

The loader includes three scripts designed to safely import Bosch products without breaking your system:

1. **Data Validator** (`validate_bosch_data.py`) - Validates JSON data before loading
2. **Standalone Loader** (`load_bosch_products.py`) - Standalone Python script
3. **Django Management Command** (`products/management/commands/load_bosch_products.py`) - Django command

## Safety Features

- ✅ **Dry Run Mode** - Preview changes without committing to database
- ✅ **Data Validation** - Comprehensive validation before processing
- ✅ **Error Handling** - Graceful handling of errors with detailed logging
- ✅ **Batch Processing** - Processes products in batches to avoid memory issues
- ✅ **Duplicate Detection** - Handles duplicate products safely
- ✅ **Image Download** - Safe image downloading with timeout and validation
- ✅ **Transaction Safety** - Uses database transactions for data integrity
- ✅ **Progress Tracking** - Real-time progress updates and statistics

## Step-by-Step Instructions

### Step 1: Validate Your Data

First, validate the JSON data to ensure it's ready for loading:

```bash
cd e-com-2024-apis
python validate_bosch_data.py --file=../bosch_products_output.json
```

This will:
- Check JSON structure and format
- Validate required fields
- Check for duplicates
- Analyze data distribution
- Show statistics about brands, categories, prices, etc.

### Step 2: Test with Dry Run

Before making any changes, run a dry run to see what would happen:

**Option A: Using standalone script**
```bash
python load_bosch_products.py --dry-run --file=../bosch_products_output.json
```

**Option B: Using Django management command**
```bash
python manage.py load_bosch_products --dry-run --file=../bosch_products_output.json
```

This will show you:
- How many products would be created/updated
- Which categories and brands would be created
- Any potential issues

### Step 3: Load the Data

Once you're satisfied with the dry run results, load the actual data:

**Option A: Using standalone script**
```bash
python load_bosch_products.py --file=../bosch_products_output.json --batch-size=25
```

**Option B: Using Django management command**
```bash
python manage.py load_bosch_products --file=../bosch_products_output.json --batch-size=25
```

## Command Options

### Validation Script Options
```bash
python validate_bosch_data.py [OPTIONS]

Options:
  --file=PATH    Path to JSON file (default: bosch_products_output.json)
```

### Loader Script Options
```bash
python load_bosch_products.py [OPTIONS]

Options:
  --dry-run           Preview changes without committing
  --force             Skip confirmation prompts
  --batch-size=N      Products per batch (default: 50)
  --file=PATH         Path to JSON file (default: bosch_products_output.json)
```

### Django Management Command Options
```bash
python manage.py load_bosch_products [OPTIONS]

Options:
  --dry-run           Preview changes without committing
  --batch-size=N      Products per batch (default: 50)
  --file=PATH         Path to JSON file (default: bosch_products_output.json)
```

## What the Loader Does

1. **Creates Categories**: Automatically creates new categories from the JSON data
2. **Creates Brands**: Automatically creates new brands from the JSON data
3. **Processes Products**: Creates or updates products with:
   - Name, description, price
   - Category and brand associations
   - Weight extraction from text (e.g., "0.8 kg (excluding battery)" → 0.8)
   - Default stock of 10 units
   - Active status set to true

4. **Downloads Images**: 
   - Downloads up to 8 images per product
   - Sets first image as primary
   - Handles download failures gracefully
   - Validates image content types

5. **Handles Duplicates**: 
   - Updates existing products with same name
   - Replaces existing images

## Data Mapping

| JSON Field | Database Field | Notes |
|------------|----------------|-------|
| `name` | `Product.name` | Required, max 1024 chars |
| `price` | `Product.price` | Required, converted to Decimal |
| `description` | `Product.description` | Optional |
| `brand` | `Product.brand` | Creates Brand if doesn't exist |
| `category` | `Product.category` | Creates Category if doesn't exist |
| `weight` | `Product.weight` | Extracted from text, in kg |
| `images` | `ProductImage` | Downloads and stores images |

## Logging and Monitoring

The loader creates detailed logs:
- **Console Output**: Real-time progress and status
- **Log File**: `bosch_products_load.log` (for standalone script)
- **Statistics**: Detailed statistics at completion

## Error Handling

The loader handles various error scenarios:
- Invalid JSON format
- Missing required fields
- Network timeouts for image downloads
- Database connection issues
- Invalid price/weight formats

## Rollback Strategy

If something goes wrong:

1. **Stop the process** (Ctrl+C)
2. **Check the logs** for specific errors
3. **Use Django admin** to review created products
4. **Delete problematic products** if needed:
   ```python
   # In Django shell
   from products.models import Product
   # Delete products created today (example)
   Product.objects.filter(created_at__date='2025-01-25').delete()
   ```

## Performance Considerations

- **Batch Size**: Smaller batches (25-50) are safer for large datasets
- **Image Downloads**: May take time depending on network speed
- **Database Load**: Uses transactions to minimize database impact
- **Memory Usage**: Processes in batches to avoid memory issues

## Troubleshooting

### Common Issues

1. **File not found**: Ensure the JSON file path is correct
2. **Permission errors**: Check file permissions and Django media directory
3. **Network timeouts**: Reduce batch size or check internet connection
4. **Database errors**: Check database connection and permissions

### Getting Help

Check the log files for detailed error messages:
- `bosch_products_load.log` - Main processing log
- Django logs in `logs/django.log` - Database and application logs

## Example Output

```
✓ Successfully loaded bosch_products_output.json
✓ Found 25 products in file
Processing batch 1/1
Created new category: Power Tools
Created new brand: Bosch
Created: BOSCH GSR 120-LI, with 2xGBA 12V 2.0Ah Battery...
Updated: BOSCH GSR 120-LI, with GBA 12V 2.0Ah Battery...

==================================================
PROCESSING STATISTICS
==================================================
Total processed: 25
Products created: 20
Products updated: 5
Errors: 0
Images downloaded: 180
Images failed: 5
==================================================
✓ All products processed successfully!
```

## Next Steps

After loading the data:
1. **Review products** in Django admin
2. **Check categories and brands** were created correctly
3. **Verify images** are displaying properly
4. **Test product pages** on your website
5. **Update stock levels** as needed
6. **Set up proper pricing** if needed

## Support

If you encounter any issues:
1. Check the validation output first
2. Run with `--dry-run` to preview changes
3. Check log files for detailed error messages
4. Start with a smaller batch size if having issues
