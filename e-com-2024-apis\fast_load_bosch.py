#!/usr/bin/env python3
"""
ULTRA-FAST Bosch Products Loader

This is an optimized version for maximum speed when loading large datasets.
Use this when you need to load data as quickly as possible.

Features:
- Bulk database operations
- Parallel image downloading
- Minimal logging for speed
- Optimized for large datasets

Usage:
    python fast_load_bosch.py [--file=bosch_products_output.json] [--workers=8]
"""

import os
import sys
import json
import requests
from decimal import Decimal
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urlparse
import re

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")

try:
    import django
    django.setup()
except Exception as e:
    print(f"Error setting up Django: {e}")
    sys.exit(1)

from django.db import transaction
from django.core.files.base import ContentFile
from products.models import Category, Brand, Product, ProductImage

class FastBoschLoader:
    """Ultra-fast loader optimized for speed"""
    
    def __init__(self, max_workers=8):
        self.max_workers = max_workers
        self.stats = {'processed': 0, 'created': 0, 'updated': 0, 'images': 0}
        
        # Pre-compile regex
        self.weight_regex = re.compile(r'(\d+\.?\d*)')
        
        # Setup HTTP session with connection pooling
        self.session = requests.Session()
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=50,
            pool_maxsize=50,
            max_retries=1
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        
        # Caches
        self.category_cache = {}
        self.brand_cache = {}
    
    def extract_weight(self, weight_str):
        """Fast weight extraction"""
        if not weight_str:
            return None
        try:
            clean = str(weight_str).lower().replace('kg', '').replace('(excluding battery)', '').strip()
            numbers = self.weight_regex.findall(clean)
            return Decimal(numbers[0]) if numbers else None
        except:
            return None
    
    def download_image(self, url):
        """Fast image download"""
        try:
            response = self.session.get(url, timeout=5, stream=True)
            response.raise_for_status()
            
            if not response.headers.get('content-type', '').startswith('image/'):
                return None
            
            filename = f"bosch_{hash(url) % 100000}.jpg"
            return ContentFile(response.content, name=filename)
        except:
            return None
    
    def get_category(self, name):
        """Get or create category with caching"""
        if name not in self.category_cache:
            category, _ = Category.objects.get_or_create(
                name=name,
                defaults={'description': f'{name} products'}
            )
            self.category_cache[name] = category
        return self.category_cache[name]
    
    def get_brand(self, name):
        """Get or create brand with caching"""
        if name not in self.brand_cache:
            brand, _ = Brand.objects.get_or_create(
                name=name,
                defaults={'description': f'{name} brand'}
            )
            self.brand_cache[name] = brand
        return self.brand_cache[name]
    
    def process_product_images_parallel(self, product, image_urls):
        """Process images in parallel"""
        if not image_urls:
            return
        
        # Clear existing images
        ProductImage.objects.filter(product=product).delete()
        
        # Download images in parallel
        with ThreadPoolExecutor(max_workers=4) as executor:
            future_to_index = {
                executor.submit(self.download_image, url): i 
                for i, url in enumerate(image_urls[:8])
            }
            
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    image_content = future.result()
                    if image_content:
                        ProductImage.objects.create(
                            product=product,
                            image=image_content,
                            is_primary=(index == 0)
                        )
                        self.stats['images'] += 1
                except:
                    pass
    
    def process_products_bulk(self, products_data):
        """Process products with bulk operations"""
        print(f"Processing {len(products_data)} products...")
        
        # Pre-create all categories and brands
        categories = set(p.get('category') for p in products_data if p.get('category'))
        brands = set(p.get('brand') for p in products_data if p.get('brand'))
        
        print("Pre-creating categories and brands...")
        for cat_name in categories:
            self.get_category(cat_name)
        for brand_name in brands:
            self.get_brand(brand_name)
        
        # Process products in batches
        batch_size = 50
        for i in range(0, len(products_data), batch_size):
            batch = products_data[i:i + batch_size]
            self.process_batch(batch)
            
            progress = min(i + batch_size, len(products_data))
            print(f"Progress: {progress}/{len(products_data)} ({progress/len(products_data)*100:.1f}%)")
    
    def process_batch(self, batch):
        """Process a batch of products"""
        with transaction.atomic():
            for product_data in batch:
                try:
                    # Validate required fields
                    if not all(product_data.get(field) for field in ['name', 'price', 'brand', 'category']):
                        continue
                    
                    # Get category and brand
                    category = self.get_category(product_data['category'])
                    brand = self.get_brand(product_data['brand'])
                    
                    # Extract weight
                    weight = self.extract_weight(product_data.get('weight'))
                    
                    # Create/update product
                    product, created = Product.objects.update_or_create(
                        name=product_data['name'],
                        defaults={
                            'description': product_data.get('description', ''),
                            'category': category,
                            'brand': brand,
                            'price': Decimal(str(product_data['price'])),
                            'stock': 10,
                            'is_active': True,
                            'weight': weight
                        }
                    )
                    
                    if created:
                        self.stats['created'] += 1
                    else:
                        self.stats['updated'] += 1
                    
                    self.stats['processed'] += 1
                    
                    # Process images in background
                    if product_data.get('images'):
                        self.process_product_images_parallel(product, product_data['images'])
                
                except Exception as e:
                    print(f"Error processing {product_data.get('name', 'Unknown')}: {e}")
    
    def load_data(self, file_path):
        """Load and process data"""
        print(f"Loading data from {file_path}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'products' not in data:
                raise ValueError("JSON must contain 'products' key")
            
            products = data['products']
            print(f"Found {len(products)} products")
            
            # Process all products
            self.process_products_bulk(products)
            
            # Print final stats
            print("\n" + "="*40)
            print("FINAL STATISTICS")
            print("="*40)
            print(f"Processed: {self.stats['processed']}")
            print(f"Created: {self.stats['created']}")
            print(f"Updated: {self.stats['updated']}")
            print(f"Images: {self.stats['images']}")
            print("="*40)
            print("✅ COMPLETED!")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
        
        return True

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Ultra-fast Bosch products loader')
    parser.add_argument('--file', default='bosch_products_output.json', help='JSON file path')
    parser.add_argument('--workers', type=int, default=8, help='Number of worker threads')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.file):
        print(f"❌ File not found: {args.file}")
        sys.exit(1)
    
    print("🚀 ULTRA-FAST BOSCH LOADER")
    print("="*40)
    
    # Confirm before proceeding
    response = input(f"Load products from {args.file}? (y/N): ")
    if response.lower() != 'y':
        print("Cancelled.")
        sys.exit(0)
    
    # Initialize and run loader
    loader = FastBoschLoader(max_workers=args.workers)
    success = loader.load_data(args.file)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
