#!/usr/bin/env python3
"""
Safe Bosch Products Data Loader

This script safely loads Bosch products from bosch_products_output.json into the database.
It includes comprehensive error handling, duplicate detection, and rollback capabilities.

Usage:
    python load_bosch_products.py [--dry-run] [--force] [--batch-size=50]

Options:
    --dry-run       : Preview changes without committing to database
    --force         : Skip confirmation prompts
    --batch-size    : Number of products to process in each batch (default: 50)
"""

import os
import sys
import json
import logging
import argparse
import requests
from decimal import Decimal, InvalidOperation
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urlparse
from pathlib import Path

# Setup Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "backend.settings")

try:
    import django
    django.setup()
except Exception as e:
    print(f"Error setting up Django: {e}")
    sys.exit(1)

from django.db import transaction, IntegrityError
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from products.models import Category, Brand, Product, ProductImage

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bosch_products_load.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BoschProductLoader:
    """Fast loader for Bosch products with performance optimizations"""

    def __init__(self, dry_run=False, batch_size=100):
        self.dry_run = dry_run
        self.batch_size = batch_size
        self.stats = {
            'processed': 0,
            'created': 0,
            'updated': 0,
            'skipped': 0,
            'errors': 0,
            'images_downloaded': 0,
            'images_failed': 0
        }
        self.errors = []

        # Cache for categories and brands to avoid repeated DB queries
        self._category_cache = {}
        self._brand_cache = {}

        # Pre-compile regex for weight extraction
        import re
        self._weight_regex = re.compile(r'(\d+\.?\d*)')

        # Session for HTTP requests (connection pooling)
        self._session = None
        
    def load_json_data(self, file_path):
        """Load and validate JSON data"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'products' not in data:
                raise ValueError("JSON file must contain 'products' key")
            
            products = data['products']
            logger.info(f"Loaded {len(products)} products from {file_path}")
            return products
            
        except FileNotFoundError:
            logger.error(f"File not found: {file_path}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in {file_path}: {e}")
            raise
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def validate_product_data(self, product_data):
        """Validate individual product data"""
        required_fields = ['name', 'price', 'brand', 'category']
        missing_fields = [field for field in required_fields if not product_data.get(field)]
        
        if missing_fields:
            return False, f"Missing required fields: {missing_fields}"
        
        # Validate price
        try:
            price = Decimal(str(product_data['price']))
            if price < 0:
                return False, "Price cannot be negative"
        except (InvalidOperation, ValueError):
            return False, f"Invalid price format: {product_data['price']}"
        
        # Validate weight if provided
        if 'weight' in product_data and product_data['weight']:
            try:
                weight_str = str(product_data['weight']).replace(' kg', '').replace('kg', '').strip()
                if weight_str and weight_str != '(excluding battery)':
                    weight = Decimal(weight_str)
                    if weight < 0:
                        return False, "Weight cannot be negative"
            except (InvalidOperation, ValueError):
                logger.warning(f"Invalid weight format for {product_data['name']}: {product_data['weight']}")
        
        return True, "Valid"
    
    def extract_weight_from_string(self, weight_str):
        """Extract numeric weight from weight string - optimized"""
        if not weight_str:
            return None

        try:
            # Fast string cleaning
            weight_clean = str(weight_str).lower().replace('kg', '').replace('(excluding battery)', '').strip()

            if not weight_clean:
                return None

            # Use pre-compiled regex for speed
            numbers = self._weight_regex.findall(weight_clean)
            if numbers:
                return Decimal(numbers[0])

        except Exception:
            pass  # Silent fail for speed

        return None
    
    def download_image(self, image_url, timeout=10):
        """Download image with error handling - optimized for speed"""
        try:
            # Use session for connection pooling
            if not hasattr(self, '_session'):
                self._session = requests.Session()
                # Configure session for better performance
                adapter = requests.adapters.HTTPAdapter(
                    pool_connections=20,
                    pool_maxsize=20,
                    max_retries=1
                )
                self._session.mount('http://', adapter)
                self._session.mount('https://', adapter)

            response = self._session.get(
                image_url,
                timeout=timeout,
                stream=True,
                headers={'User-Agent': 'Mozilla/5.0 (compatible; ProductLoader/1.0)'}
            )
            response.raise_for_status()

            # Quick content type check
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                return None

            # Get filename from URL
            parsed_url = urlparse(image_url)
            filename = os.path.basename(parsed_url.path)
            if not filename or '.' not in filename:
                filename = f"bosch_{hash(image_url) % 10000}.jpg"

            return ContentFile(response.content, name=filename)

        except requests.RequestException:
            return None
        except Exception:
            return None
    
    def get_or_create_category(self, category_name):
        """Get or create category with caching for performance"""
        if category_name in self._category_cache:
            return self._category_cache[category_name]

        try:
            category, created = Category.objects.get_or_create(
                name=category_name,
                defaults={'description': f'Category for {category_name} products'}
            )
            if created:
                logger.info(f"Created new category: {category_name}")

            # Cache for future use
            self._category_cache[category_name] = category
            return category
        except Exception as e:
            logger.error(f"Error creating category '{category_name}': {e}")
            raise

    def get_or_create_brand(self, brand_name):
        """Get or create brand with caching for performance"""
        if brand_name in self._brand_cache:
            return self._brand_cache[brand_name]

        try:
            brand, created = Brand.objects.get_or_create(
                name=brand_name,
                defaults={'description': f'{brand_name} brand products'}
            )
            if created:
                logger.info(f"Created new brand: {brand_name}")

            # Cache for future use
            self._brand_cache[brand_name] = brand
            return brand
        except Exception as e:
            logger.error(f"Error creating brand '{brand_name}': {e}")
            raise

    def process_product(self, product_data):
        """Process a single product with comprehensive error handling"""
        try:
            # Validate product data
            is_valid, validation_msg = self.validate_product_data(product_data)
            if not is_valid:
                logger.error(f"Invalid product data for '{product_data.get('name', 'Unknown')}': {validation_msg}")
                self.stats['errors'] += 1
                self.errors.append(f"Validation error: {validation_msg}")
                return False

            # Get or create category and brand
            category = self.get_or_create_category(product_data['category'])
            brand = self.get_or_create_brand(product_data['brand'])

            # Extract weight
            weight = self.extract_weight_from_string(product_data.get('weight'))

            # Prepare product data
            product_defaults = {
                'description': product_data.get('description', ''),
                'category': category,
                'brand': brand,
                'price': Decimal(str(product_data['price'])),
                'stock': 10,  # Default stock
                'is_active': True,
                'weight': weight
            }

            if self.dry_run:
                logger.info(f"[DRY RUN] Would process product: {product_data['name']}")
                self.stats['processed'] += 1
                return True

            # Create or update product
            with transaction.atomic():
                product, created = Product.objects.update_or_create(
                    name=product_data['name'],
                    defaults=product_defaults
                )

                if created:
                    self.stats['created'] += 1
                    logger.info(f"Created product: {product.name}")
                else:
                    self.stats['updated'] += 1
                    logger.info(f"Updated product: {product.name}")

                # Process images
                self.process_product_images(product, product_data.get('images', []))

            self.stats['processed'] += 1
            return True

        except Exception as e:
            logger.error(f"Error processing product '{product_data.get('name', 'Unknown')}': {e}")
            self.stats['errors'] += 1
            self.errors.append(f"Product processing error: {e}")
            return False

    def process_product_images(self, product, image_urls):
        """Process images for a product with parallel downloading"""
        if not image_urls:
            return

        # Remove existing images if updating
        if not self.dry_run:
            ProductImage.objects.filter(product=product).delete()

        if self.dry_run:
            for image_url in image_urls[:8]:
                logger.info(f"[DRY RUN] Would download image: {image_url}")
            return

        # Download images in parallel for speed
        image_urls_limited = image_urls[:8]  # Limit to 8 images

        with ThreadPoolExecutor(max_workers=4) as executor:
            # Submit all download tasks
            future_to_url = {
                executor.submit(self.download_image, url): (i, url)
                for i, url in enumerate(image_urls_limited)
            }

            # Process completed downloads
            for future in as_completed(future_to_url):
                i, image_url = future_to_url[future]
                try:
                    image_content = future.result()
                    if image_content:
                        ProductImage.objects.create(
                            product=product,
                            image=image_content,
                            is_primary=(i == 0)  # First image is primary
                        )
                        self.stats['images_downloaded'] += 1
                    else:
                        self.stats['images_failed'] += 1

                except Exception as e:
                    logger.warning(f"Failed to process image {image_url}: {e}")
                    self.stats['images_failed'] += 1

    def process_batch(self, products_batch):
        """Process a batch of products with bulk operations for speed"""
        logger.info(f"Processing batch of {len(products_batch)} products...")

        if self.dry_run:
            # Fast dry run processing
            for product_data in products_batch:
                logger.info(f"[DRY RUN] Would process: {product_data.get('name', 'Unknown')}")
                self.stats['processed'] += 1
            return

        # Process products with bulk operations where possible
        for product_data in products_batch:
            try:
                self.process_product(product_data)
            except Exception as e:
                logger.error(f"Batch processing error: {e}")
                self.stats['errors'] += 1

    def load_products(self, file_path):
        """Main method to load all products"""
        try:
            # Load data
            products = self.load_json_data(file_path)
            total_products = len(products)

            logger.info(f"Starting to process {total_products} products...")
            logger.info(f"Dry run mode: {self.dry_run}")

            # Process in batches
            for i in range(0, total_products, self.batch_size):
                batch = products[i:i + self.batch_size]
                batch_num = (i // self.batch_size) + 1
                total_batches = (total_products + self.batch_size - 1) // self.batch_size

                logger.info(f"Processing batch {batch_num}/{total_batches}")
                self.process_batch(batch)

                # Progress update
                progress = (i + len(batch)) / total_products * 100
                logger.info(f"Progress: {progress:.1f}% ({i + len(batch)}/{total_products})")

            # Final statistics
            self.print_statistics()

            if self.stats['errors'] > 0:
                logger.warning(f"Completed with {self.stats['errors']} errors. Check logs for details.")
                return False
            else:
                logger.info("All products processed successfully!")
                return True

        except Exception as e:
            logger.error(f"Fatal error during processing: {e}")
            return False

    def print_statistics(self):
        """Print processing statistics"""
        logger.info("=" * 50)
        logger.info("PROCESSING STATISTICS")
        logger.info("=" * 50)
        logger.info(f"Total processed: {self.stats['processed']}")
        logger.info(f"Products created: {self.stats['created']}")
        logger.info(f"Products updated: {self.stats['updated']}")
        logger.info(f"Products skipped: {self.stats['skipped']}")
        logger.info(f"Errors: {self.stats['errors']}")
        logger.info(f"Images downloaded: {self.stats['images_downloaded']}")
        logger.info(f"Images failed: {self.stats['images_failed']}")
        logger.info("=" * 50)

        if self.errors:
            logger.info("ERRORS ENCOUNTERED:")
            for error in self.errors[:10]:  # Show first 10 errors
                logger.info(f"  - {error}")
            if len(self.errors) > 10:
                logger.info(f"  ... and {len(self.errors) - 10} more errors")


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(description='Load Bosch products into database')
    parser.add_argument('--dry-run', action='store_true',
                       help='Preview changes without committing to database')
    parser.add_argument('--force', action='store_true',
                       help='Skip confirmation prompts')
    parser.add_argument('--batch-size', type=int, default=50,
                       help='Number of products to process in each batch')
    parser.add_argument('--file', type=str, default='bosch_products_output.json',
                       help='Path to the JSON file containing product data')

    args = parser.parse_args()

    # Validate file exists
    if not os.path.exists(args.file):
        logger.error(f"File not found: {args.file}")
        sys.exit(1)

    # Confirmation prompt
    if not args.force and not args.dry_run:
        response = input(f"This will load products from {args.file} into the database. Continue? (y/N): ")
        if response.lower() != 'y':
            logger.info("Operation cancelled by user.")
            sys.exit(0)

    # Initialize loader
    loader = BoschProductLoader(dry_run=args.dry_run, batch_size=args.batch_size)

    # Load products
    success = loader.load_products(args.file)

    if success:
        logger.info("Product loading completed successfully!")
        sys.exit(0)
    else:
        logger.error("Product loading completed with errors!")
        sys.exit(1)


if __name__ == "__main__":
    main()
