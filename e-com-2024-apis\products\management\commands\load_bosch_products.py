"""
Django Management Command for Loading Bosch Products

Usage:
    python manage.py load_bosch_products [--dry-run] [--file=path/to/file.json]
"""

import json
import logging
from decimal import Decimal
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from products.models import Category, Brand, Product, ProductImage
import requests
from django.core.files.base import ContentFile
from urllib.parse import urlparse
from django.utils.text import slugify
import os
import uuid

class Command(BaseCommand):
    help = 'Load Bosch products from JSON file into database'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            default='bosch_products_output.json',
            help='Path to JSON file containing product data'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Preview changes without committing to database'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of products to process in each batch'
        )
        parser.add_argument(
            '--fast',
            action='store_true',
            help='Enable fast mode with optimizations'
        )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.stats = {
            'processed': 0,
            'created': 0,
            'updated': 0,
            'errors': 0,
            'images_downloaded': 0,
            'images_failed': 0
        }
        # Performance optimizations
        self._category_cache = {}
        self._brand_cache = {}
        self._session = None

        # Pre-compile regex for weight extraction
        import re
        self._weight_regex = re.compile(r'(\d+\.?\d*)')
    
    def handle(self, *args, **options):
        """Main command handler"""
        self.dry_run = options['dry_run']
        self.batch_size = options['batch_size']
        file_path = options['file']
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        try:
            # Load and validate data
            products = self.load_json_data(file_path)
            
            self.stdout.write(
                self.style.SUCCESS(f'Loaded {len(products)} products from {file_path}')
            )
            
            if self.dry_run:
                self.stdout.write(
                    self.style.WARNING('DRY RUN MODE - No changes will be made to database')
                )
            
            # Process products
            self.process_products(products)
            
            # Print final statistics
            self.print_statistics()
            
        except Exception as e:
            raise CommandError(f'Error processing products: {e}')
    
    def load_json_data(self, file_path):
        """Load and validate JSON data"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'products' not in data:
                raise CommandError("JSON file must contain 'products' key")
            
            return data['products']
            
        except FileNotFoundError:
            raise CommandError(f"File not found: {file_path}")
        except json.JSONDecodeError as e:
            raise CommandError(f"Invalid JSON in {file_path}: {e}")
    
    def extract_weight_from_string(self, weight_str):
        """Extract numeric weight from weight string - optimized"""
        if not weight_str:
            return None

        try:
            weight_clean = str(weight_str).lower().replace('kg', '').replace('(excluding battery)', '').strip()

            if not weight_clean:
                return None

            # Use pre-compiled regex for speed
            numbers = self._weight_regex.findall(weight_clean)
            if numbers:
                return Decimal(numbers[0])

        except Exception:
            pass

        return None
    
    def download_image(self, image_url, timeout=10):
        """Download image with error handling - optimized for speed"""
        try:
            # Use session for connection pooling
            if not self._session:
                self._session = requests.Session()
                adapter = requests.adapters.HTTPAdapter(
                    pool_connections=20,
                    pool_maxsize=20,
                    max_retries=1
                )
                self._session.mount('http://', adapter)
                self._session.mount('https://', adapter)

            response = self._session.get(
                image_url,
                timeout=timeout,
                stream=True,
                headers={'User-Agent': 'Mozilla/5.0 (compatible; ProductLoader/1.0)'}
            )
            response.raise_for_status()

            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                return None

            parsed_url = urlparse(image_url)
            filename = os.path.basename(parsed_url.path)
            if not filename or '.' not in filename:
                filename = f"bosch_{hash(image_url) % 10000}.jpg"

            return ContentFile(response.content, name=filename)

        except Exception:
            return None
    
    def get_or_create_category(self, category_name):
        """Get or create category with caching"""
        if category_name in self._category_cache:
            return self._category_cache[category_name]

        category, created = Category.objects.get_or_create(
            name=category_name,
            defaults={'description': f'Category for {category_name} products'}
        )
        if created:
            self.stdout.write(f"Created new category: {category_name}")

        self._category_cache[category_name] = category
        return category

    def get_or_create_brand(self, brand_name):
        """Get or create brand with caching"""
        if brand_name in self._brand_cache:
            return self._brand_cache[brand_name]

        brand, created = Brand.objects.get_or_create(
            name=brand_name,
            defaults={'description': f'{brand_name} brand products'}
        )
        if created:
            self.stdout.write(f"Created new brand: {brand_name}")

        self._brand_cache[brand_name] = brand
        return brand
    
    def process_product(self, product_data):
        """Process a single product - NEVER skip any product"""
        try:
            # Get product name with fallback
            product_name = product_data.get('name') or f"Bosch Product {hash(str(product_data)) % 10000}"

            # Get category and brand with fallbacks
            category_name = product_data.get('category') or 'Power Tools'
            brand_name = product_data.get('brand') or 'Bosch'

            category = self.get_or_create_category(category_name)
            brand = self.get_or_create_brand(brand_name)

            # Extract weight
            weight = self.extract_weight_from_string(product_data.get('weight'))

            # Handle price with fallback
            try:
                price = Decimal(str(product_data.get('price', 0)))
                if price < 0:
                    price = Decimal('0')
            except:
                price = Decimal('0')
                self.stdout.write(
                    self.style.WARNING(f"Using price 0 for '{product_name}' due to invalid price")
                )

            # Prepare product data
            product_defaults = {
                'description': product_data.get('description', ''),
                'category': category,
                'brand': brand,
                'price': price,
                'stock': 10,  # Default stock
                'is_active': True,
                'weight': weight
            }

            if self.dry_run:
                self.stdout.write(f"[DRY RUN] Would process: {product_name}")
                self.stats['processed'] += 1
                return True

            # Create product with unique slug handling
            with transaction.atomic():
                # Generate unique slug
                base_slug = slugify(product_name)
                unique_slug = base_slug
                counter = 1

                # Ensure slug is unique
                while Product.objects.filter(slug=unique_slug).exists():
                    unique_slug = f"{base_slug}-{counter}"
                    counter += 1

                product = Product.objects.create(
                    name=product_name,
                    slug=unique_slug,
                    **product_defaults
                )

                self.stats['created'] += 1
                self.stdout.write(
                    self.style.SUCCESS(f"Created: {product.name}")
                )

                # Process images
                self.process_product_images(product, product_data.get('images', []))

            self.stats['processed'] += 1
            return True

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Error processing '{product_data.get('name', 'Unknown')}': {e}")
            )

            # Try to create a basic product even on error
            try:
                error_name = product_data.get('name', f"Error Product {self.stats['errors']}")
                error_slug = slugify(error_name)

                # Ensure unique slug for error product
                counter = 1
                unique_error_slug = error_slug
                while Product.objects.filter(slug=unique_error_slug).exists():
                    unique_error_slug = f"{error_slug}-error-{counter}"
                    counter += 1

                error_product = Product.objects.create(
                    name=error_name,
                    slug=unique_error_slug,
                    description=f"Error during import: {str(e)}",
                    category=self.get_or_create_category('Power Tools'),
                    brand=self.get_or_create_brand('Bosch'),
                    price=Decimal('0'),
                    stock=0,
                    is_active=False
                )
                self.stdout.write(f"Created error placeholder: {error_product.name}")
                self.stats['created'] += 1
            except:
                pass

            self.stats['errors'] += 1
            self.stats['processed'] += 1  # Still count as processed
            return True  # Don't fail the batch
    
    def process_product_images(self, product, image_urls):
        """Process images for a product with parallel downloading"""
        if not image_urls:
            return

        # Remove existing images if updating
        ProductImage.objects.filter(product=product).delete()

        # Download images in parallel for speed
        image_urls_limited = image_urls[:8]  # Limit to 8 images

        from concurrent.futures import ThreadPoolExecutor, as_completed
        with ThreadPoolExecutor(max_workers=4) as executor:
            # Submit all download tasks
            future_to_url = {
                executor.submit(self.download_image, url): (i, url)
                for i, url in enumerate(image_urls_limited)
            }

            # Process completed downloads
            for future in as_completed(future_to_url):
                i, image_url = future_to_url[future]
                try:
                    image_content = future.result()
                    if image_content:
                        ProductImage.objects.create(
                            product=product,
                            image=image_content,
                            is_primary=(i == 0)  # First image is primary
                        )
                        self.stats['images_downloaded'] += 1
                    else:
                        self.stats['images_failed'] += 1

                except Exception:
                    self.stats['images_failed'] += 1
    
    def process_products(self, products):
        """Process all products in batches"""
        total_products = len(products)
        
        for i in range(0, total_products, self.batch_size):
            batch = products[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (total_products + self.batch_size - 1) // self.batch_size
            
            self.stdout.write(f"Processing batch {batch_num}/{total_batches}")
            
            for product_data in batch:
                self.process_product(product_data)
            
            # Progress update
            progress = min(i + self.batch_size, total_products)
            self.stdout.write(f"Progress: {progress}/{total_products} products")
    
    def print_statistics(self):
        """Print processing statistics"""
        self.stdout.write("\n" + "="*50)
        self.stdout.write("PROCESSING STATISTICS")
        self.stdout.write("="*50)
        self.stdout.write(f"Total processed: {self.stats['processed']}")
        self.stdout.write(f"Products created: {self.stats['created']}")
        self.stdout.write(f"Products updated: {self.stats['updated']}")
        self.stdout.write(f"Errors: {self.stats['errors']}")
        self.stdout.write(f"Images downloaded: {self.stats['images_downloaded']}")
        self.stdout.write(f"Images failed: {self.stats['images_failed']}")
        self.stdout.write("="*50)
        
        if self.stats['errors'] == 0:
            self.stdout.write(
                self.style.SUCCESS("✓ All products processed successfully!")
            )
        else:
            self.stdout.write(
                self.style.WARNING(f"⚠ Completed with {self.stats['errors']} errors")
            )
