"""
Django Management Command for Loading Bosch Products

Usage:
    python manage.py load_bosch_products [--dry-run] [--file=path/to/file.json]
"""

import json
import logging
from decimal import Decimal
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from products.models import Category, Brand, Product, ProductImage
import requests
from django.core.files.base import ContentFile
from urllib.parse import urlparse
import os

class Command(BaseCommand):
    help = 'Load Bosch products from JSON file into database'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--file',
            type=str,
            default='bosch_products_output.json',
            help='Path to JSON file containing product data'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Preview changes without committing to database'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=50,
            help='Number of products to process in each batch'
        )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.stats = {
            'processed': 0,
            'created': 0,
            'updated': 0,
            'errors': 0,
            'images_downloaded': 0,
            'images_failed': 0
        }
    
    def handle(self, *args, **options):
        """Main command handler"""
        self.dry_run = options['dry_run']
        self.batch_size = options['batch_size']
        file_path = options['file']
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        try:
            # Load and validate data
            products = self.load_json_data(file_path)
            
            self.stdout.write(
                self.style.SUCCESS(f'Loaded {len(products)} products from {file_path}')
            )
            
            if self.dry_run:
                self.stdout.write(
                    self.style.WARNING('DRY RUN MODE - No changes will be made to database')
                )
            
            # Process products
            self.process_products(products)
            
            # Print final statistics
            self.print_statistics()
            
        except Exception as e:
            raise CommandError(f'Error processing products: {e}')
    
    def load_json_data(self, file_path):
        """Load and validate JSON data"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'products' not in data:
                raise CommandError("JSON file must contain 'products' key")
            
            return data['products']
            
        except FileNotFoundError:
            raise CommandError(f"File not found: {file_path}")
        except json.JSONDecodeError as e:
            raise CommandError(f"Invalid JSON in {file_path}: {e}")
    
    def extract_weight_from_string(self, weight_str):
        """Extract numeric weight from weight string"""
        if not weight_str:
            return None
            
        try:
            import re
            weight_clean = str(weight_str).lower()
            weight_clean = weight_clean.replace('kg', '').replace('(excluding battery)', '').strip()
            
            if not weight_clean:
                return None
                
            numbers = re.findall(r'\d+\.?\d*', weight_clean)
            if numbers:
                return Decimal(numbers[0])
                
        except Exception:
            pass
        
        return None
    
    def download_image(self, image_url, timeout=30):
        """Download image with error handling"""
        try:
            response = requests.get(image_url, timeout=timeout, stream=True)
            response.raise_for_status()
            
            content_type = response.headers.get('content-type', '')
            if not content_type.startswith('image/'):
                return None
            
            parsed_url = urlparse(image_url)
            filename = os.path.basename(parsed_url.path)
            if not filename or '.' not in filename:
                filename = f"bosch_image_{hash(image_url) % 10000}.jpg"
            
            return ContentFile(response.content, name=filename)
            
        except Exception as e:
            self.logger.warning(f"Failed to download image {image_url}: {e}")
            return None
    
    def get_or_create_category(self, category_name):
        """Get or create category"""
        category, created = Category.objects.get_or_create(
            name=category_name,
            defaults={'description': f'Category for {category_name} products'}
        )
        if created:
            self.stdout.write(f"Created new category: {category_name}")
        return category
    
    def get_or_create_brand(self, brand_name):
        """Get or create brand"""
        brand, created = Brand.objects.get_or_create(
            name=brand_name,
            defaults={'description': f'{brand_name} brand products'}
        )
        if created:
            self.stdout.write(f"Created new brand: {brand_name}")
        return brand
    
    def process_product(self, product_data):
        """Process a single product"""
        try:
            # Validate required fields
            required_fields = ['name', 'price', 'brand', 'category']
            for field in required_fields:
                if not product_data.get(field):
                    raise ValueError(f"Missing required field: {field}")
            
            # Get or create category and brand
            category = self.get_or_create_category(product_data['category'])
            brand = self.get_or_create_brand(product_data['brand'])
            
            # Extract weight
            weight = self.extract_weight_from_string(product_data.get('weight'))
            
            # Prepare product data
            product_defaults = {
                'description': product_data.get('description', ''),
                'category': category,
                'brand': brand,
                'price': Decimal(str(product_data['price'])),
                'stock': 10,  # Default stock
                'is_active': True,
                'weight': weight
            }
            
            if self.dry_run:
                self.stdout.write(f"[DRY RUN] Would process: {product_data['name']}")
                self.stats['processed'] += 1
                return True
            
            # Create or update product
            with transaction.atomic():
                product, created = Product.objects.update_or_create(
                    name=product_data['name'],
                    defaults=product_defaults
                )
                
                if created:
                    self.stats['created'] += 1
                    self.stdout.write(
                        self.style.SUCCESS(f"Created: {product.name}")
                    )
                else:
                    self.stats['updated'] += 1
                    self.stdout.write(f"Updated: {product.name}")
                
                # Process images
                self.process_product_images(product, product_data.get('images', []))
            
            self.stats['processed'] += 1
            return True
            
        except Exception as e:
            self.stats['errors'] += 1
            self.stdout.write(
                self.style.ERROR(f"Error processing '{product_data.get('name', 'Unknown')}': {e}")
            )
            return False
    
    def process_product_images(self, product, image_urls):
        """Process images for a product"""
        if not image_urls:
            return
        
        # Remove existing images if updating
        ProductImage.objects.filter(product=product).delete()
        
        for i, image_url in enumerate(image_urls[:8]):  # Limit to 8 images
            try:
                image_content = self.download_image(image_url)
                if image_content:
                    ProductImage.objects.create(
                        product=product,
                        image=image_content,
                        is_primary=(i == 0)  # First image is primary
                    )
                    self.stats['images_downloaded'] += 1
                else:
                    self.stats['images_failed'] += 1
                    
            except Exception as e:
                self.stats['images_failed'] += 1
                self.logger.warning(f"Failed to process image for {product.name}: {e}")
    
    def process_products(self, products):
        """Process all products in batches"""
        total_products = len(products)
        
        for i in range(0, total_products, self.batch_size):
            batch = products[i:i + self.batch_size]
            batch_num = (i // self.batch_size) + 1
            total_batches = (total_products + self.batch_size - 1) // self.batch_size
            
            self.stdout.write(f"Processing batch {batch_num}/{total_batches}")
            
            for product_data in batch:
                self.process_product(product_data)
            
            # Progress update
            progress = min(i + self.batch_size, total_products)
            self.stdout.write(f"Progress: {progress}/{total_products} products")
    
    def print_statistics(self):
        """Print processing statistics"""
        self.stdout.write("\n" + "="*50)
        self.stdout.write("PROCESSING STATISTICS")
        self.stdout.write("="*50)
        self.stdout.write(f"Total processed: {self.stats['processed']}")
        self.stdout.write(f"Products created: {self.stats['created']}")
        self.stdout.write(f"Products updated: {self.stats['updated']}")
        self.stdout.write(f"Errors: {self.stats['errors']}")
        self.stdout.write(f"Images downloaded: {self.stats['images_downloaded']}")
        self.stdout.write(f"Images failed: {self.stats['images_failed']}")
        self.stdout.write("="*50)
        
        if self.stats['errors'] == 0:
            self.stdout.write(
                self.style.SUCCESS("✓ All products processed successfully!")
            )
        else:
            self.stdout.write(
                self.style.WARNING(f"⚠ Completed with {self.stats['errors']} errors")
            )
