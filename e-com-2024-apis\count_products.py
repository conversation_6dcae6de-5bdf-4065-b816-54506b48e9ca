#!/usr/bin/env python3
"""
Simple Product Counter

Counts products in JSON file and shows basic info to verify before loading.
"""

import json
import sys

def count_products(file_path):
    """Count products in JSON file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if 'products' not in data:
            print("❌ Error: JSON file must contain 'products' key")
            return False
        
        products = data['products']
        total = len(products)
        
        print("📊 PRODUCT COUNT VERIFICATION")
        print("="*40)
        print(f"File: {file_path}")
        print(f"Total products: {total}")
        
        # Show first few product names
        print("\nFirst 5 products:")
        for i, product in enumerate(products[:5]):
            name = product.get('name', 'No name')
            price = product.get('price', 'No price')
            print(f"  {i+1}. {name} - ₹{price}")
        
        if total > 5:
            print(f"  ... and {total-5} more products")
        
        # Check for required fields
        missing_names = sum(1 for p in products if not p.get('name'))
        missing_prices = sum(1 for p in products if not p.get('price'))
        missing_brands = sum(1 for p in products if not p.get('brand'))
        missing_categories = sum(1 for p in products if not p.get('category'))
        
        print(f"\nField completeness:")
        print(f"  Products with names: {total - missing_names}/{total}")
        print(f"  Products with prices: {total - missing_prices}/{total}")
        print(f"  Products with brands: {total - missing_brands}/{total}")
        print(f"  Products with categories: {total - missing_categories}/{total}")
        
        # Count images
        total_images = sum(len(p.get('images', [])) for p in products)
        products_with_images = sum(1 for p in products if p.get('images'))
        
        print(f"\nImages:")
        print(f"  Products with images: {products_with_images}/{total}")
        print(f"  Total images to download: {total_images}")
        
        print("\n" + "="*40)
        print(f"✅ Ready to load {total} products")
        
        return True
        
    except FileNotFoundError:
        print(f"❌ Error: File not found: {file_path}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ Error: Invalid JSON: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    file_path = sys.argv[1] if len(sys.argv) > 1 else 'bosch_products_output.json'
    count_products(file_path)

if __name__ == "__main__":
    main()
