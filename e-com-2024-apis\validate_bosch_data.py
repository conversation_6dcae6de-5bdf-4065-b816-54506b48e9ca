#!/usr/bin/env python3
"""
Bosch Products Data Validator

This script validates the bosch_products_output.json file before loading into the database.
It checks for data integrity, duplicates, and potential issues.

Usage:
    python validate_bosch_data.py [--file=bosch_products_output.json]
"""

import json
import sys
import argparse
from decimal import Decimal, InvalidOperation
from collections import Counter
import re

def validate_json_structure(data):
    """Validate basic JSON structure"""
    issues = []
    
    if not isinstance(data, dict):
        issues.append("Root element must be an object")
        return issues
    
    if 'products' not in data:
        issues.append("Missing 'products' key in root object")
        return issues
    
    if not isinstance(data['products'], list):
        issues.append("'products' must be an array")
        return issues
    
    if len(data['products']) == 0:
        issues.append("'products' array is empty")
    
    return issues

def validate_product(product, index):
    """Validate individual product data"""
    issues = []
    
    # Required fields
    required_fields = ['name', 'price', 'brand', 'category']
    for field in required_fields:
        if field not in product:
            issues.append(f"Product {index}: Missing required field '{field}'")
        elif not product[field]:
            issues.append(f"Product {index}: Empty value for required field '{field}'")
    
    # Validate name
    if 'name' in product:
        name = product['name']
        if len(name) > 1024:
            issues.append(f"Product {index}: Name too long ({len(name)} chars, max 1024)")
        if len(name.strip()) == 0:
            issues.append(f"Product {index}: Name is empty or whitespace only")
    
    # Validate price
    if 'price' in product:
        try:
            price = Decimal(str(product['price']))
            if price < 0:
                issues.append(f"Product {index}: Price cannot be negative ({price})")
            if price > 999999999.99:
                issues.append(f"Product {index}: Price too large ({price})")
        except (InvalidOperation, ValueError):
            issues.append(f"Product {index}: Invalid price format '{product['price']}'")
    
    # Validate weight if present
    if 'weight' in product and product['weight']:
        weight_str = str(product['weight'])
        # Try to extract numeric value
        weight_clean = weight_str.lower().replace('kg', '').replace('(excluding battery)', '').strip()
        if weight_clean:
            numbers = re.findall(r'\d+\.?\d*', weight_clean)
            if numbers:
                try:
                    weight = Decimal(numbers[0])
                    if weight < 0:
                        issues.append(f"Product {index}: Weight cannot be negative ({weight})")
                    if weight > 1000:
                        issues.append(f"Product {index}: Weight seems too large ({weight} kg)")
                except (InvalidOperation, ValueError):
                    issues.append(f"Product {index}: Could not parse weight '{weight_str}'")
    
    # Validate images
    if 'images' in product:
        if not isinstance(product['images'], list):
            issues.append(f"Product {index}: 'images' must be an array")
        else:
            for img_index, image_url in enumerate(product['images']):
                if not isinstance(image_url, str):
                    issues.append(f"Product {index}: Image {img_index} must be a string")
                elif not image_url.startswith(('http://', 'https://')):
                    issues.append(f"Product {index}: Image {img_index} must be a valid URL")
    
    # Validate description length
    if 'description' in product and product['description']:
        desc_len = len(product['description'])
        if desc_len > 5000:  # Reasonable limit for text field
            issues.append(f"Product {index}: Description too long ({desc_len} chars)")
    
    return issues

def check_duplicates(products):
    """Check for duplicate products"""
    issues = []
    
    # Check duplicate names
    names = [p.get('name', '') for p in products]
    name_counts = Counter(names)
    duplicates = {name: count for name, count in name_counts.items() if count > 1 and name}
    
    if duplicates:
        issues.append("Duplicate product names found:")
        for name, count in duplicates.items():
            issues.append(f"  - '{name}' appears {count} times")
    
    return issues

def analyze_data_distribution(products):
    """Analyze data distribution for insights"""
    analysis = {}
    
    # Brand distribution
    brands = [p.get('brand', '') for p in products if p.get('brand')]
    analysis['brands'] = dict(Counter(brands).most_common())
    
    # Category distribution
    categories = [p.get('category', '') for p in products if p.get('category')]
    analysis['categories'] = dict(Counter(categories).most_common())
    
    # Price statistics
    prices = []
    for p in products:
        try:
            if p.get('price'):
                prices.append(float(p['price']))
        except (ValueError, TypeError):
            pass
    
    if prices:
        analysis['price_stats'] = {
            'min': min(prices),
            'max': max(prices),
            'avg': sum(prices) / len(prices),
            'count': len(prices)
        }
    
    # Image statistics
    image_counts = [len(p.get('images', [])) for p in products]
    if image_counts:
        analysis['image_stats'] = {
            'min_images': min(image_counts),
            'max_images': max(image_counts),
            'avg_images': sum(image_counts) / len(image_counts),
            'total_images': sum(image_counts)
        }
    
    return analysis

def print_analysis(analysis):
    """Print data analysis results"""
    print("\n" + "="*50)
    print("DATA ANALYSIS")
    print("="*50)
    
    if 'brands' in analysis:
        print(f"\nBrands ({len(analysis['brands'])}):")
        for brand, count in list(analysis['brands'].items())[:10]:
            print(f"  - {brand}: {count} products")
        if len(analysis['brands']) > 10:
            print(f"  ... and {len(analysis['brands']) - 10} more brands")
    
    if 'categories' in analysis:
        print(f"\nCategories ({len(analysis['categories'])}):")
        for category, count in list(analysis['categories'].items())[:10]:
            print(f"  - {category}: {count} products")
        if len(analysis['categories']) > 10:
            print(f"  ... and {len(analysis['categories']) - 10} more categories")
    
    if 'price_stats' in analysis:
        stats = analysis['price_stats']
        print(f"\nPrice Statistics:")
        print(f"  - Min: ₹{stats['min']:,.2f}")
        print(f"  - Max: ₹{stats['max']:,.2f}")
        print(f"  - Average: ₹{stats['avg']:,.2f}")
        print(f"  - Products with prices: {stats['count']}")
    
    if 'image_stats' in analysis:
        stats = analysis['image_stats']
        print(f"\nImage Statistics:")
        print(f"  - Min images per product: {stats['min_images']}")
        print(f"  - Max images per product: {stats['max_images']}")
        print(f"  - Average images per product: {stats['avg_images']:.1f}")
        print(f"  - Total images to download: {stats['total_images']}")

def main():
    """Main validation function"""
    parser = argparse.ArgumentParser(description='Validate Bosch products JSON data')
    parser.add_argument('--file', type=str, default='bosch_products_output.json',
                       help='Path to the JSON file to validate')
    
    args = parser.parse_args()
    
    # Load JSON file
    try:
        with open(args.file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✓ Successfully loaded {args.file}")
    except FileNotFoundError:
        print(f"✗ Error: File not found: {args.file}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"✗ Error: Invalid JSON in {args.file}: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"✗ Error loading file: {e}")
        sys.exit(1)
    
    # Validate structure
    structure_issues = validate_json_structure(data)
    if structure_issues:
        print("\n✗ STRUCTURE ISSUES:")
        for issue in structure_issues:
            print(f"  - {issue}")
        sys.exit(1)
    
    products = data['products']
    print(f"✓ Found {len(products)} products in file")
    
    # Validate each product
    all_issues = []
    for i, product in enumerate(products):
        issues = validate_product(product, i + 1)
        all_issues.extend(issues)
    
    # Check for duplicates
    duplicate_issues = check_duplicates(products)
    all_issues.extend(duplicate_issues)
    
    # Print validation results
    if all_issues:
        print(f"\n✗ VALIDATION ISSUES FOUND ({len(all_issues)}):")
        for issue in all_issues[:20]:  # Show first 20 issues
            print(f"  - {issue}")
        if len(all_issues) > 20:
            print(f"  ... and {len(all_issues) - 20} more issues")
        print(f"\nRecommendation: Fix these issues before loading data into database")
    else:
        print("\n✓ All validation checks passed!")
    
    # Data analysis
    analysis = analyze_data_distribution(products)
    print_analysis(analysis)
    
    # Summary
    print("\n" + "="*50)
    print("VALIDATION SUMMARY")
    print("="*50)
    print(f"Total products: {len(products)}")
    print(f"Validation issues: {len(all_issues)}")
    print(f"Status: {'❌ FAILED' if all_issues else '✅ PASSED'}")
    
    if all_issues:
        sys.exit(1)
    else:
        print("\n✓ Data is ready for loading into database!")
        sys.exit(0)

if __name__ == "__main__":
    main()
